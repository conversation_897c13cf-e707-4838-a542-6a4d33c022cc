{"version": 3, "sources": ["../../../../src/run/ios/XcodeBuild.ts"], "sourcesContent": ["import { ExpoRunFormatter } from '@expo/xcpretty';\nimport chalk from 'chalk';\nimport { spawn, SpawnOptionsWithoutStdio } from 'child_process';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\nimport { BuildProps, ProjectInfo } from './XcodeBuild.types';\nimport { ensureDeviceIsCodeSignedForDeploymentAsync } from './codeSigning/configureCodeSigning';\nimport { simulatorBuildRequiresCodeSigning } from './codeSigning/simulatorCodeSigning';\nimport * as Log from '../../log';\nimport { ensureDirectory } from '../../utils/dir';\nimport { env } from '../../utils/env';\nimport { AbortCommandError, CommandError } from '../../utils/errors';\nimport { getUserTerminal } from '../../utils/terminal';\nexport function logPrettyItem(message: string) {\n  Log.log(chalk`{whiteBright \\u203A} ${message}`);\n}\n\nexport function matchEstimatedBinaryPath(buildOutput: string): string | null {\n  // Match the full path that contains `/(.*)/Developer/Xcode/DerivedData/(.*)/Build/Products/(.*)/(.*).app`\n  const appBinaryPathMatch = buildOutput.match(\n    /(\\/(?:\\\\\\s|[^ ])+\\/Developer\\/Xcode\\/DerivedData\\/(?:\\\\\\s|[^ ])+\\/Build\\/Products\\/(?:Debug|Release)-(?:[^\\s/]+)\\/(?:\\\\\\s|[^ ])+\\.app)/\n  );\n  if (!appBinaryPathMatch?.length) {\n    throw new CommandError(\n      'XCODE_BUILD',\n      `Malformed xcodebuild results: app binary path was not generated in build output. Please report this issue and run your project with Xcode instead.`\n    );\n  } else {\n    // Sort for the shortest\n    const shortestPath = (appBinaryPathMatch.filter((a) => typeof a === 'string' && a) as string[])\n      .sort((a: string, b: string) => a.length - b.length)[0]\n      .trim();\n\n    Log.debug(`Found app binary path: ${shortestPath}`);\n    return shortestPath;\n  }\n}\n/**\n *\n * @returns '/Users/<USER>/Library/Developer/Xcode/DerivedData/myapp-gpgjqjodrxtervaufttwnsgimhrx/Build/Products/Debug-iphonesimulator/myapp.app'\n */\nexport function getAppBinaryPath(buildOutput: string) {\n  // Matches what's used in \"Bundle React Native code and images\" script.\n  // Requires that `-hideShellScriptEnvironment` is not included in the build command (extra logs).\n\n  try {\n    // Like `\\=/Users/<USER>/Library/Developer/Xcode/DerivedData/Exponent-anpuosnglkxokahjhfszejloqfvo/Build/Products/Debug-iphonesimulator`\n    const CONFIGURATION_BUILD_DIR = extractEnvVariableFromBuild(\n      buildOutput,\n      'CONFIGURATION_BUILD_DIR'\n    ).sort(\n      // Longer name means more suffixes, we want the shortest possible one to be first.\n      // Massive projects (like Expo Go) can sometimes print multiple different sets of environment variables.\n      // This can become an issue with some\n      (a, b) => a.length - b.length\n    );\n    // Like `Exponent.app`\n    const UNLOCALIZED_RESOURCES_FOLDER_PATH = extractEnvVariableFromBuild(\n      buildOutput,\n      'UNLOCALIZED_RESOURCES_FOLDER_PATH'\n    );\n\n    const binaryPath = path.join(\n      // Use the shortest defined env variable (usually there's just one).\n      CONFIGURATION_BUILD_DIR[0],\n      // Use the last defined env variable.\n      UNLOCALIZED_RESOURCES_FOLDER_PATH[UNLOCALIZED_RESOURCES_FOLDER_PATH.length - 1]\n    );\n\n    // If the app has a space in the name it'll fail because it isn't escaped properly by Xcode.\n    return getEscapedPath(binaryPath);\n  } catch (error) {\n    if (error instanceof CommandError && error.code === 'XCODE_BUILD') {\n      const possiblePath = matchEstimatedBinaryPath(buildOutput);\n      if (possiblePath) {\n        return possiblePath;\n      }\n    }\n    throw error;\n  }\n}\n\nexport function getEscapedPath(filePath: string): string {\n  if (fs.existsSync(filePath)) {\n    return filePath;\n  }\n  const unescapedPath = filePath.split(/\\\\ /).join(' ');\n  if (fs.existsSync(unescapedPath)) {\n    return unescapedPath;\n  }\n  throw new CommandError(\n    'XCODE_BUILD',\n    `Unexpected: Generated app at path \"${filePath}\" cannot be read, the app cannot be installed. Please report this and build onto a simulator.`\n  );\n}\n\nexport function extractEnvVariableFromBuild(buildOutput: string, variableName: string) {\n  // Xcode can sometimes escape `=` with a backslash or put the value in quotes\n  const reg = new RegExp(`export ${variableName}\\\\\\\\?=(.*)$`, 'mg');\n  const matched = [...buildOutput.matchAll(reg)];\n\n  if (!matched || !matched.length) {\n    throw new CommandError(\n      'XCODE_BUILD',\n      `Malformed xcodebuild results: \"${variableName}\" variable was not generated in build output. Please report this issue and run your project with Xcode instead.`\n    );\n  }\n  return matched.map((value) => value[1]).filter(Boolean) as string[];\n}\n\nexport function getProcessOptions({\n  packager,\n  shouldSkipInitialBundling,\n  terminal,\n  port,\n  eagerBundleOptions,\n}: {\n  packager: boolean;\n  shouldSkipInitialBundling?: boolean;\n  terminal: string | undefined;\n  port: number;\n  eagerBundleOptions?: string;\n}): SpawnOptionsWithoutStdio {\n  const SKIP_BUNDLING = shouldSkipInitialBundling ? '1' : undefined;\n  if (packager) {\n    return {\n      env: {\n        ...process.env,\n        RCT_TERMINAL: terminal,\n        SKIP_BUNDLING,\n        RCT_METRO_PORT: port.toString(),\n        __EXPO_EAGER_BUNDLE_OPTIONS: eagerBundleOptions,\n      },\n    };\n  }\n\n  return {\n    env: {\n      ...process.env,\n      RCT_TERMINAL: terminal,\n      SKIP_BUNDLING,\n      __EXPO_EAGER_BUNDLE_OPTIONS: eagerBundleOptions,\n      // Always skip launching the packager from a build script.\n      // The script is used for people building their project directly from Xcode.\n      // This essentially means \"› Running script 'Start Packager'\" does nothing.\n      RCT_NO_LAUNCH_PACKAGER: 'true',\n      // FORCE_BUNDLING: '0'\n    },\n  };\n}\n\nexport async function getXcodeBuildArgsAsync(\n  props: Pick<\n    BuildProps,\n    | 'buildCache'\n    | 'projectRoot'\n    | 'xcodeProject'\n    | 'configuration'\n    | 'scheme'\n    | 'device'\n    | 'isSimulator'\n  >\n): Promise<string[]> {\n  const args = [\n    props.xcodeProject.isWorkspace ? '-workspace' : '-project',\n    props.xcodeProject.name,\n    '-configuration',\n    props.configuration,\n    '-scheme',\n    props.scheme,\n    '-destination',\n    `id=${props.device.udid}`,\n  ];\n\n  if (!props.isSimulator || simulatorBuildRequiresCodeSigning(props.projectRoot)) {\n    const developmentTeamId = await ensureDeviceIsCodeSignedForDeploymentAsync(props.projectRoot);\n    if (developmentTeamId) {\n      args.push(\n        `DEVELOPMENT_TEAM=${developmentTeamId}`,\n        '-allowProvisioningUpdates',\n        '-allowProvisioningDeviceRegistration'\n      );\n    }\n  }\n\n  // Add last\n  if (props.buildCache === false) {\n    args.push(\n      // Will first clean the derived data folder.\n      'clean',\n      // Then build step must be added otherwise the process will simply clean and exit.\n      'build'\n    );\n  }\n  return args;\n}\n\nfunction spawnXcodeBuild(\n  args: string[],\n  options: SpawnOptionsWithoutStdio,\n  { onData }: { onData: (data: string) => void }\n): Promise<{ code: number | null; results: string; error: string }> {\n  const buildProcess = spawn('xcodebuild', args, options);\n\n  let results = '';\n  let error = '';\n\n  buildProcess.stdout.on('data', (data: Buffer) => {\n    const stringData = data.toString();\n    results += stringData;\n    onData(stringData);\n  });\n\n  buildProcess.stderr.on('data', (data: Buffer) => {\n    const stringData = data instanceof Buffer ? data.toString() : data;\n    error += stringData;\n  });\n\n  return new Promise(async (resolve, reject) => {\n    buildProcess.on('close', (code: number) => {\n      resolve({ code, results, error });\n    });\n  });\n}\n\nasync function spawnXcodeBuildWithFlush(\n  args: string[],\n  options: SpawnOptionsWithoutStdio,\n  { onFlush }: { onFlush: (data: string) => void }\n): Promise<{ code: number | null; results: string; error: string }> {\n  let currentBuffer = '';\n\n  // Data can be sent in chunks that would have no relevance to our regex\n  // this can cause massive slowdowns, so we need to ensure the data is complete before attempting to parse it.\n  function flushBuffer() {\n    if (!currentBuffer) {\n      return;\n    }\n\n    const data = currentBuffer;\n    // Reset buffer.\n    currentBuffer = '';\n    // Process data.\n    onFlush(data);\n  }\n\n  const data = await spawnXcodeBuild(args, options, {\n    onData(stringData) {\n      currentBuffer += stringData;\n      // Only flush the data if we have a full line.\n      if (currentBuffer.endsWith(os.EOL)) {\n        flushBuffer();\n      }\n    },\n  });\n\n  // Flush log data at the end just in case we missed something.\n  flushBuffer();\n  return data;\n}\n\nasync function spawnXcodeBuildWithFormat(\n  args: string[],\n  options: SpawnOptionsWithoutStdio,\n  { projectRoot, xcodeProject }: { projectRoot: string; xcodeProject: ProjectInfo }\n): Promise<{ code: number | null; results: string; error: string; formatter: ExpoRunFormatter }> {\n  Log.debug(`  xcodebuild ${args.join(' ')}`);\n\n  logPrettyItem(chalk.bold`Planning build`);\n\n  const formatter = ExpoRunFormatter.create(projectRoot, {\n    xcodeProject,\n    isDebug: env.EXPO_DEBUG,\n  });\n\n  const results = await spawnXcodeBuildWithFlush(args, options, {\n    onFlush(data) {\n      // Process data.\n      for (const line of formatter.pipe(data)) {\n        // Log parsed results.\n        Log.log(line);\n      }\n    },\n  });\n\n  Log.debug(`Exited with code: ${results.code}`);\n\n  if (\n    // User cancelled with ctrl-c\n    results.code === null ||\n    // Build interrupted\n    results.code === 75\n  ) {\n    throw new AbortCommandError();\n  }\n\n  Log.log(formatter.getBuildSummary());\n\n  return { ...results, formatter };\n}\n\nexport async function buildAsync(props: BuildProps): Promise<string> {\n  const args = await getXcodeBuildArgsAsync(props);\n\n  const { projectRoot, xcodeProject, shouldSkipInitialBundling, port, eagerBundleOptions } = props;\n\n  const { code, results, formatter, error } = await spawnXcodeBuildWithFormat(\n    args,\n    getProcessOptions({\n      packager: false,\n      terminal: getUserTerminal(),\n      shouldSkipInitialBundling,\n      port,\n      eagerBundleOptions,\n    }),\n    {\n      projectRoot,\n      xcodeProject,\n    }\n  );\n\n  const logFilePath = writeBuildLogs(projectRoot, results, error);\n\n  if (code !== 0) {\n    // Determine if the logger found any errors;\n    const wasErrorPresented = !!formatter.errors.length;\n\n    if (wasErrorPresented) {\n      // This has a flaw, if the user is missing a file, and there is a script error, only the missing file error will be shown.\n      // They will only see the script error if they fix the missing file and rerun.\n      // The flaw can be fixed by catching script errors in the custom logger.\n      throw new CommandError(\n        `Failed to build iOS project. \"xcodebuild\" exited with error code ${code}.`\n      );\n    }\n\n    _assertXcodeBuildResults(code, results, error, xcodeProject, logFilePath);\n  }\n  return results;\n}\n\n// Exposed for testing.\nexport function _assertXcodeBuildResults(\n  code: number | null,\n  results: string,\n  error: string,\n  xcodeProject: { name: string },\n  logFilePath: string\n): void {\n  const errorTitle = `Failed to build iOS project. \"xcodebuild\" exited with error code ${code}.`;\n\n  const throwWithMessage = (message: string): never => {\n    throw new CommandError(\n      `${errorTitle}\\nTo view more error logs, try building the app with Xcode directly, by opening ${xcodeProject.name}.\\n\\n` +\n        message +\n        `Build logs written to ${chalk.underline(logFilePath)}`\n    );\n  };\n\n  const localizedError = error.match(/NSLocalizedFailure = \"(.*)\"/)?.[1];\n\n  if (localizedError) {\n    throwWithMessage(chalk.bold(localizedError) + '\\n\\n');\n  }\n  // Show all the log info because often times the error is coming from a shell script,\n  // that invoked a node script, that started metro, which threw an error.\n\n  throwWithMessage(results + '\\n\\n' + error);\n}\n\nfunction writeBuildLogs(projectRoot: string, buildOutput: string, errorOutput: string) {\n  const [logFilePath, errorFilePath] = getErrorLogFilePath(projectRoot);\n\n  fs.writeFileSync(logFilePath, buildOutput);\n  fs.writeFileSync(errorFilePath, errorOutput);\n  return logFilePath;\n}\n\nfunction getErrorLogFilePath(projectRoot: string): [string, string] {\n  const folder = path.join(projectRoot, '.expo');\n  ensureDirectory(folder);\n  return [path.join(folder, 'xcodebuild.log'), path.join(folder, 'xcodebuild-error.log')];\n}\n"], "names": ["_assertXcodeBuildResults", "buildAsync", "extractEnvVariableFromBuild", "getAppBinaryPath", "getEscapedPath", "getProcessOptions", "getXcodeBuildArgsAsync", "logPrettyItem", "matchEstimatedBinaryPath", "message", "Log", "log", "chalk", "buildOutput", "appBinaryPathMatch", "match", "length", "CommandError", "shortestPath", "filter", "a", "sort", "b", "trim", "debug", "CONFIGURATION_BUILD_DIR", "UNLOCALIZED_RESOURCES_FOLDER_PATH", "binaryPath", "path", "join", "error", "code", "<PERSON><PERSON><PERSON>", "filePath", "fs", "existsSync", "unescapedPath", "split", "variableName", "reg", "RegExp", "matched", "matchAll", "map", "value", "Boolean", "packager", "shouldSkipInitialBundling", "terminal", "port", "eagerBundleOptions", "SKIP_BUNDLING", "undefined", "env", "process", "RCT_TERMINAL", "RCT_METRO_PORT", "toString", "__EXPO_EAGER_BUNDLE_OPTIONS", "RCT_NO_LAUNCH_PACKAGER", "props", "args", "xcodeProject", "isWorkspace", "name", "configuration", "scheme", "device", "udid", "isSimulator", "simulatorBuildRequiresCodeSigning", "projectRoot", "developmentTeamId", "ensureDeviceIsCodeSignedForDeploymentAsync", "push", "buildCache", "spawnXcodeBuild", "options", "onData", "buildProcess", "spawn", "results", "stdout", "on", "data", "stringData", "stderr", "<PERSON><PERSON><PERSON>", "Promise", "resolve", "reject", "spawnXcodeBuildWithFlush", "onFlush", "current<PERSON><PERSON><PERSON>", "flushBuffer", "endsWith", "os", "EOL", "spawnXcodeBuildWithFormat", "bold", "formatter", "ExpoRunFormatter", "create", "isDebug", "EXPO_DEBUG", "line", "pipe", "AbortCommandError", "getBuildSummary", "getUserTerminal", "logFilePath", "writeBuildLogs", "wasErrorPresented", "errors", "errorTitle", "throwWithMessage", "underline", "localizedError", "errorOutput", "errorFilePath", "getErrorLogFilePath", "writeFileSync", "folder", "ensureDirectory"], "mappings": ";;;;;;;;;;;IAwVgBA,wBAAwB;eAAxBA;;IAzCMC,UAAU;eAAVA;;IA7MNC,2BAA2B;eAA3BA;;IAvDAC,gBAAgB;eAAhBA;;IAyCAC,cAAc;eAAdA;;IA4BAC,iBAAiB;eAAjBA;;IAyCMC,sBAAsB;eAAtBA;;IA1INC,aAAa;eAAbA;;IAIAC,wBAAwB;eAAxBA;;;;yBAnBiB;;;;;;;gEACf;;;;;;;yBAC8B;;;;;;;gEACjC;;;;;;;gEACA;;;;;;;gEACE;;;;;;sCAG0C;sCACT;6DAC7B;qBACW;qBACZ;wBAC4B;0BAChB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACzB,SAASD,cAAcE,OAAe;IAC3CC,KAAIC,GAAG,CAACC,IAAAA,gBAAK,CAAA,CAAC,qBAAqB,EAAEH,QAAQ,CAAC;AAChD;AAEO,SAASD,yBAAyBK,WAAmB;IAC1D,0GAA0G;IAC1G,MAAMC,qBAAqBD,YAAYE,KAAK,CAC1C;IAEF,IAAI,EAACD,sCAAAA,mBAAoBE,MAAM,GAAE;QAC/B,MAAM,IAAIC,oBAAY,CACpB,eACA,CAAC,kJAAkJ,CAAC;IAExJ,OAAO;QACL,wBAAwB;QACxB,MAAMC,eAAe,AAACJ,mBAAmBK,MAAM,CAAC,CAACC,IAAM,OAAOA,MAAM,YAAYA,GAC7EC,IAAI,CAAC,CAACD,GAAWE,IAAcF,EAAEJ,MAAM,GAAGM,EAAEN,MAAM,CAAC,CAAC,EAAE,CACtDO,IAAI;QAEPb,KAAIc,KAAK,CAAC,CAAC,uBAAuB,EAAEN,cAAc;QAClD,OAAOA;IACT;AACF;AAKO,SAASf,iBAAiBU,WAAmB;IAClD,uEAAuE;IACvE,iGAAiG;IAEjG,IAAI;QACF,2IAA2I;QAC3I,MAAMY,0BAA0BvB,4BAC9BW,aACA,2BACAQ,IAAI,CACJ,kFAAkF;QAClF,wGAAwG;QACxG,qCAAqC;QACrC,CAACD,GAAGE,IAAMF,EAAEJ,MAAM,GAAGM,EAAEN,MAAM;QAE/B,sBAAsB;QACtB,MAAMU,oCAAoCxB,4BACxCW,aACA;QAGF,MAAMc,aAAaC,eAAI,CAACC,IAAI,CAC1B,oEAAoE;QACpEJ,uBAAuB,CAAC,EAAE,EAC1B,qCAAqC;QACrCC,iCAAiC,CAACA,kCAAkCV,MAAM,GAAG,EAAE;QAGjF,4FAA4F;QAC5F,OAAOZ,eAAeuB;IACxB,EAAE,OAAOG,OAAO;QACd,IAAIA,iBAAiBb,oBAAY,IAAIa,MAAMC,IAAI,KAAK,eAAe;YACjE,MAAMC,eAAexB,yBAAyBK;YAC9C,IAAImB,cAAc;gBAChB,OAAOA;YACT;QACF;QACA,MAAMF;IACR;AACF;AAEO,SAAS1B,eAAe6B,QAAgB;IAC7C,IAAIC,aAAE,CAACC,UAAU,CAACF,WAAW;QAC3B,OAAOA;IACT;IACA,MAAMG,gBAAgBH,SAASI,KAAK,CAAC,OAAOR,IAAI,CAAC;IACjD,IAAIK,aAAE,CAACC,UAAU,CAACC,gBAAgB;QAChC,OAAOA;IACT;IACA,MAAM,IAAInB,oBAAY,CACpB,eACA,CAAC,mCAAmC,EAAEgB,SAAS,6FAA6F,CAAC;AAEjJ;AAEO,SAAS/B,4BAA4BW,WAAmB,EAAEyB,YAAoB;IACnF,6EAA6E;IAC7E,MAAMC,MAAM,IAAIC,OAAO,CAAC,OAAO,EAAEF,aAAa,WAAW,CAAC,EAAE;IAC5D,MAAMG,UAAU;WAAI5B,YAAY6B,QAAQ,CAACH;KAAK;IAE9C,IAAI,CAACE,WAAW,CAACA,QAAQzB,MAAM,EAAE;QAC/B,MAAM,IAAIC,oBAAY,CACpB,eACA,CAAC,+BAA+B,EAAEqB,aAAa,+GAA+G,CAAC;IAEnK;IACA,OAAOG,QAAQE,GAAG,CAAC,CAACC,QAAUA,KAAK,CAAC,EAAE,EAAEzB,MAAM,CAAC0B;AACjD;AAEO,SAASxC,kBAAkB,EAChCyC,QAAQ,EACRC,yBAAyB,EACzBC,QAAQ,EACRC,IAAI,EACJC,kBAAkB,EAOnB;IACC,MAAMC,gBAAgBJ,4BAA4B,MAAMK;IACxD,IAAIN,UAAU;QACZ,OAAO;YACLO,KAAK;gBACH,GAAGC,QAAQD,GAAG;gBACdE,cAAcP;gBACdG;gBACAK,gBAAgBP,KAAKQ,QAAQ;gBAC7BC,6BAA6BR;YAC/B;QACF;IACF;IAEA,OAAO;QACLG,KAAK;YACH,GAAGC,QAAQD,GAAG;YACdE,cAAcP;YACdG;YACAO,6BAA6BR;YAC7B,0DAA0D;YAC1D,4EAA4E;YAC5E,2EAA2E;YAC3ES,wBAAwB;QAE1B;IACF;AACF;AAEO,eAAerD,uBACpBsD,KASC;IAED,MAAMC,OAAO;QACXD,MAAME,YAAY,CAACC,WAAW,GAAG,eAAe;QAChDH,MAAME,YAAY,CAACE,IAAI;QACvB;QACAJ,MAAMK,aAAa;QACnB;QACAL,MAAMM,MAAM;QACZ;QACA,CAAC,GAAG,EAAEN,MAAMO,MAAM,CAACC,IAAI,EAAE;KAC1B;IAED,IAAI,CAACR,MAAMS,WAAW,IAAIC,IAAAA,uDAAiC,EAACV,MAAMW,WAAW,GAAG;QAC9E,MAAMC,oBAAoB,MAAMC,IAAAA,gEAA0C,EAACb,MAAMW,WAAW;QAC5F,IAAIC,mBAAmB;YACrBX,KAAKa,IAAI,CACP,CAAC,iBAAiB,EAAEF,mBAAmB,EACvC,6BACA;QAEJ;IACF;IAEA,WAAW;IACX,IAAIZ,MAAMe,UAAU,KAAK,OAAO;QAC9Bd,KAAKa,IAAI,CACP,4CAA4C;QAC5C,SACA,kFAAkF;QAClF;IAEJ;IACA,OAAOb;AACT;AAEA,SAASe,gBACPf,IAAc,EACdgB,OAAiC,EACjC,EAAEC,MAAM,EAAsC;IAE9C,MAAMC,eAAeC,IAAAA,sBAAK,EAAC,cAAcnB,MAAMgB;IAE/C,IAAII,UAAU;IACd,IAAInD,QAAQ;IAEZiD,aAAaG,MAAM,CAACC,EAAE,CAAC,QAAQ,CAACC;QAC9B,MAAMC,aAAaD,KAAK3B,QAAQ;QAChCwB,WAAWI;QACXP,OAAOO;IACT;IAEAN,aAAaO,MAAM,CAACH,EAAE,CAAC,QAAQ,CAACC;QAC9B,MAAMC,aAAaD,gBAAgBG,SAASH,KAAK3B,QAAQ,KAAK2B;QAC9DtD,SAASuD;IACX;IAEA,OAAO,IAAIG,QAAQ,OAAOC,SAASC;QACjCX,aAAaI,EAAE,CAAC,SAAS,CAACpD;YACxB0D,QAAQ;gBAAE1D;gBAAMkD;gBAASnD;YAAM;QACjC;IACF;AACF;AAEA,eAAe6D,yBACb9B,IAAc,EACdgB,OAAiC,EACjC,EAAEe,OAAO,EAAuC;IAEhD,IAAIC,gBAAgB;IAEpB,uEAAuE;IACvE,6GAA6G;IAC7G,SAASC;QACP,IAAI,CAACD,eAAe;YAClB;QACF;QAEA,MAAMT,OAAOS;QACb,gBAAgB;QAChBA,gBAAgB;QAChB,gBAAgB;QAChBD,QAAQR;IACV;IAEA,MAAMA,OAAO,MAAMR,gBAAgBf,MAAMgB,SAAS;QAChDC,QAAOO,UAAU;YACfQ,iBAAiBR;YACjB,8CAA8C;YAC9C,IAAIQ,cAAcE,QAAQ,CAACC,aAAE,CAACC,GAAG,GAAG;gBAClCH;YACF;QACF;IACF;IAEA,8DAA8D;IAC9DA;IACA,OAAOV;AACT;AAEA,eAAec,0BACbrC,IAAc,EACdgB,OAAiC,EACjC,EAAEN,WAAW,EAAET,YAAY,EAAsD;IAEjFpD,KAAIc,KAAK,CAAC,CAAC,aAAa,EAAEqC,KAAKhC,IAAI,CAAC,MAAM;IAE1CtB,cAAcK,gBAAK,CAACuF,IAAI,CAAC,cAAc,CAAC;IAExC,MAAMC,YAAYC,4BAAgB,CAACC,MAAM,CAAC/B,aAAa;QACrDT;QACAyC,SAASlD,QAAG,CAACmD,UAAU;IACzB;IAEA,MAAMvB,UAAU,MAAMU,yBAAyB9B,MAAMgB,SAAS;QAC5De,SAAQR,IAAI;YACV,gBAAgB;YAChB,KAAK,MAAMqB,QAAQL,UAAUM,IAAI,CAACtB,MAAO;gBACvC,sBAAsB;gBACtB1E,KAAIC,GAAG,CAAC8F;YACV;QACF;IACF;IAEA/F,KAAIc,KAAK,CAAC,CAAC,kBAAkB,EAAEyD,QAAQlD,IAAI,EAAE;IAE7C,IACE,6BAA6B;IAC7BkD,QAAQlD,IAAI,KAAK,QACjB,oBAAoB;IACpBkD,QAAQlD,IAAI,KAAK,IACjB;QACA,MAAM,IAAI4E,yBAAiB;IAC7B;IAEAjG,KAAIC,GAAG,CAACyF,UAAUQ,eAAe;IAEjC,OAAO;QAAE,GAAG3B,OAAO;QAAEmB;IAAU;AACjC;AAEO,eAAenG,WAAW2D,KAAiB;IAChD,MAAMC,OAAO,MAAMvD,uBAAuBsD;IAE1C,MAAM,EAAEW,WAAW,EAAET,YAAY,EAAEf,yBAAyB,EAAEE,IAAI,EAAEC,kBAAkB,EAAE,GAAGU;IAE3F,MAAM,EAAE7B,IAAI,EAAEkD,OAAO,EAAEmB,SAAS,EAAEtE,KAAK,EAAE,GAAG,MAAMoE,0BAChDrC,MACAxD,kBAAkB;QAChByC,UAAU;QACVE,UAAU6D,IAAAA,yBAAe;QACzB9D;QACAE;QACAC;IACF,IACA;QACEqB;QACAT;IACF;IAGF,MAAMgD,cAAcC,eAAexC,aAAaU,SAASnD;IAEzD,IAAIC,SAAS,GAAG;QACd,4CAA4C;QAC5C,MAAMiF,oBAAoB,CAAC,CAACZ,UAAUa,MAAM,CAACjG,MAAM;QAEnD,IAAIgG,mBAAmB;YACrB,0HAA0H;YAC1H,8EAA8E;YAC9E,wEAAwE;YACxE,MAAM,IAAI/F,oBAAY,CACpB,CAAC,iEAAiE,EAAEc,KAAK,CAAC,CAAC;QAE/E;QAEA/B,yBAAyB+B,MAAMkD,SAASnD,OAAOgC,cAAcgD;IAC/D;IACA,OAAO7B;AACT;AAGO,SAASjF,yBACd+B,IAAmB,EACnBkD,OAAe,EACfnD,KAAa,EACbgC,YAA8B,EAC9BgD,WAAmB;QAYIhF;IAVvB,MAAMoF,aAAa,CAAC,iEAAiE,EAAEnF,KAAK,CAAC,CAAC;IAE9F,MAAMoF,mBAAmB,CAAC1G;QACxB,MAAM,IAAIQ,oBAAY,CACpB,GAAGiG,WAAW,gFAAgF,EAAEpD,aAAaE,IAAI,CAAC,KAAK,CAAC,GACtHvD,UACA,CAAC,sBAAsB,EAAEG,gBAAK,CAACwG,SAAS,CAACN,cAAc;IAE7D;IAEA,MAAMO,kBAAiBvF,eAAAA,MAAMf,KAAK,CAAC,mDAAZe,YAA4C,CAAC,EAAE;IAEtE,IAAIuF,gBAAgB;QAClBF,iBAAiBvG,gBAAK,CAACuF,IAAI,CAACkB,kBAAkB;IAChD;IACA,qFAAqF;IACrF,wEAAwE;IAExEF,iBAAiBlC,UAAU,SAASnD;AACtC;AAEA,SAASiF,eAAexC,WAAmB,EAAE1D,WAAmB,EAAEyG,WAAmB;IACnF,MAAM,CAACR,aAAaS,cAAc,GAAGC,oBAAoBjD;IAEzDrC,aAAE,CAACuF,aAAa,CAACX,aAAajG;IAC9BqB,aAAE,CAACuF,aAAa,CAACF,eAAeD;IAChC,OAAOR;AACT;AAEA,SAASU,oBAAoBjD,WAAmB;IAC9C,MAAMmD,SAAS9F,eAAI,CAACC,IAAI,CAAC0C,aAAa;IACtCoD,IAAAA,oBAAe,EAACD;IAChB,OAAO;QAAC9F,eAAI,CAACC,IAAI,CAAC6F,QAAQ;QAAmB9F,eAAI,CAACC,IAAI,CAAC6F,QAAQ;KAAwB;AACzF"}