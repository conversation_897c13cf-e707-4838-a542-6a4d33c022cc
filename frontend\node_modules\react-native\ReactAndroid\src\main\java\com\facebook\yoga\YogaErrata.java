/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py

package com.facebook.yoga;

public enum YogaErrata {
  NONE(0),
  STRETCH_FLEX_BASIS(1),
  ABSOLUTE_POSITION_WITHOUT_INSETS_EXCLUDES_PADDING(2),
  ABSOLUTE_PERCENT_AGAINST_INNER_SIZE(4),
  ALL(**********),
  CLASSIC(**********);

  private final int mIntValue;

  YogaErrata(int intValue) {
    mIntValue = intValue;
  }

  public int intValue() {
    return mIntValue;
  }

  public static YogaErrata fromInt(int value) {
    switch (value) {
      case 0: return NONE;
      case 1: return STRETCH_FLEX_BASIS;
      case 2: return ABSOLUTE_POSITION_WITHOUT_INSETS_EXCLUDES_PADDING;
      case 4: return ABSOLUTE_PERCENT_AGAINST_INNER_SIZE;
      case **********: return ALL;
      case **********: return CLASS<PERSON>;
      default: throw new IllegalArgumentException("Unknown enum value: " + value);
    }
  }
}
