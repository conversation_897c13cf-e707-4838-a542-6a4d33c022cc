{"version": 3, "sources": ["../../../../../src/start/platforms/android/getDevices.ts"], "sourcesContent": ["import { Device, getAttachedDevicesAsync } from './adb';\nimport { listAvdsAsync } from './emulator';\nimport { CommandError } from '../../../utils/errors';\n\n/** Get a list of all devices including offline emulators. Asserts if no devices are available. */\nexport async function getDevicesAsync(): Promise<Device[]> {\n  const bootedDevices = await getAttachedDevicesAsync();\n\n  const data = await listAvdsAsync();\n  const connectedNames = bootedDevices.map(({ name }) => name);\n\n  const offlineEmulators = data\n    .filter(({ name }) => !connectedNames.includes(name))\n    .map(({ name, type }) => {\n      return {\n        name,\n        type,\n        isBooted: false,\n        // TODO: Are emulators always authorized?\n        isAuthorized: true,\n      };\n    });\n\n  const allDevices = bootedDevices.concat(offlineEmulators);\n\n  if (!allDevices.length) {\n    throw new CommandError(\n      [\n        `No Android connected device found, and no emulators could be started automatically.`,\n        `Please connect a device or create an emulator (https://docs.expo.dev/workflow/android-studio-emulator).`,\n        `Then follow the instructions here to enable USB debugging:`,\n        `https://developer.android.com/studio/run/device.html#developer-device-options. If you are using Genymotion go to Settings -> ADB, select \"Use custom Android SDK tools\", and point it at your Android SDK directory.`,\n      ].join('\\n')\n    );\n  }\n\n  return allDevices;\n}\n"], "names": ["getDevicesAsync", "bootedDevices", "getAttachedDevicesAsync", "data", "listAvdsAsync", "connectedNames", "map", "name", "offlineEmulators", "filter", "includes", "type", "isBooted", "isAuthorized", "allDevices", "concat", "length", "CommandError", "join"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;qBAL0B;0BAClB;wBACD;AAGtB,eAAeA;IACpB,MAAMC,gBAAgB,MAAMC,IAAAA,4BAAuB;IAEnD,MAAMC,OAAO,MAAMC,IAAAA,uBAAa;IAChC,MAAMC,iBAAiBJ,cAAcK,GAAG,CAAC,CAAC,EAAEC,IAAI,EAAE,GAAKA;IAEvD,MAAMC,mBAAmBL,KACtBM,MAAM,CAAC,CAAC,EAAEF,IAAI,EAAE,GAAK,CAACF,eAAeK,QAAQ,CAACH,OAC9CD,GAAG,CAAC,CAAC,EAAEC,IAAI,EAAEI,IAAI,EAAE;QAClB,OAAO;YACLJ;YACAI;YACAC,UAAU;YACV,yCAAyC;YACzCC,cAAc;QAChB;IACF;IAEF,MAAMC,aAAab,cAAcc,MAAM,CAACP;IAExC,IAAI,CAACM,WAAWE,MAAM,EAAE;QACtB,MAAM,IAAIC,oBAAY,CACpB;YACE,CAAC,mFAAmF,CAAC;YACrF,CAAC,uGAAuG,CAAC;YACzG,CAAC,0DAA0D,CAAC;YAC5D,CAAC,oNAAoN,CAAC;SACvN,CAACC,IAAI,CAAC;IAEX;IAEA,OAAOJ;AACT"}